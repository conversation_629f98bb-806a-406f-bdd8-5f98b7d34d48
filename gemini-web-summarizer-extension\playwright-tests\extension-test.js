/**
 * Playwright测试套件 - Gemini Web Summarizer扩展
 * 使用Playwright MCP服务进行自动化测试
 */

const { test, expect, chromium } = require('@playwright/test');
const path = require('path');

// 扩展路径
const EXTENSION_PATH = path.resolve(__dirname, '..');

// 测试配置
const TEST_CONFIG = {
    // 测试用的网页URL
    testPageUrl: 'https://example.com',
    // 模拟API密钥（用于UI测试，不会实际调用API）
    mockApiKey: 'AIza' + 'x'.repeat(35),
    // 测试超时时间
    timeout: 30000
};

/**
 * 启动带扩展的浏览器
 */
async function launchBrowserWithExtension() {
    const browser = await chromium.launchPersistentContext('', {
        headless: false, // 显示浏览器窗口以便观察
        args: [
            `--disable-extensions-except=${EXTENSION_PATH}`,
            `--load-extension=${EXTENSION_PATH}`,
            '--no-sandbox',
            '--disable-dev-shm-usage'
        ],
        viewport: { width: 1280, height: 720 }
    });
    
    return browser;
}

/**
 * 获取扩展ID
 */
async function getExtensionId(context) {
    // 访问扩展管理页面
    const page = await context.newPage();
    await page.goto('chrome://extensions/');
    
    // 启用开发者模式
    await page.locator('#devMode').check();
    
    // 查找扩展ID
    const extensionCards = await page.locator('extensions-item').all();
    let extensionId = null;
    
    for (const card of extensionCards) {
        const name = await card.locator('#name').textContent();
        if (name && name.includes('Gemini Web Summarizer')) {
            extensionId = await card.getAttribute('id');
            break;
        }
    }
    
    await page.close();
    return extensionId;
}

// 测试套件开始
test.describe('Gemini Web Summarizer 扩展测试', () => {
    let browser;
    let extensionId;
    
    // 测试前准备
    test.beforeAll(async () => {
        browser = await launchBrowserWithExtension();
        extensionId = await getExtensionId(browser);
        console.log('扩展ID:', extensionId);
    });
    
    // 测试后清理
    test.afterAll(async () => {
        if (browser) {
            await browser.close();
        }
    });

    test('1. 扩展加载测试', async () => {
        test.setTimeout(TEST_CONFIG.timeout);
        
        // 验证扩展已加载
        expect(extensionId).toBeTruthy();
        
        // 访问扩展管理页面验证扩展状态
        const page = await browser.newPage();
        await page.goto('chrome://extensions/');
        
        const extensionCard = page.locator(`extensions-item[id="${extensionId}"]`);
        await expect(extensionCard).toBeVisible();
        
        // 检查扩展是否启用
        const toggleButton = extensionCard.locator('#enableToggle');
        const isEnabled = await toggleButton.isChecked();
        expect(isEnabled).toBe(true);
        
        await page.close();
    });

    test('2. 弹出窗口UI测试', async () => {
        test.setTimeout(TEST_CONFIG.timeout);
        
        const page = await browser.newPage();
        await page.goto(TEST_CONFIG.testPageUrl);
        
        // 点击扩展图标打开弹出窗口
        const extensionUrl = `chrome-extension://${extensionId}/popup.html`;
        await page.goto(extensionUrl);
        
        // 验证弹出窗口元素
        await expect(page.locator('h1')).toContainText('Gemini Web Summarizer');
        await expect(page.locator('#summarize-btn')).toBeVisible();
        await expect(page.locator('#generate-flowchart-btn')).toBeVisible();
        
        // 验证标签页切换
        await page.locator('#summary-tab').click();
        await expect(page.locator('#summary-content')).toBeVisible();
        
        await page.locator('#flowchart-tab').click();
        await expect(page.locator('#flowchart-content')).toBeVisible();
        
        await page.close();
    });

    test('3. 设置页面测试', async () => {
        test.setTimeout(TEST_CONFIG.timeout);
        
        const page = await browser.newPage();
        const optionsUrl = `chrome-extension://${extensionId}/options.html`;
        await page.goto(optionsUrl);
        
        // 验证设置页面元素
        await expect(page.locator('h1')).toContainText('设置');
        await expect(page.locator('#api-key')).toBeVisible();
        await expect(page.locator('#save-settings')).toBeVisible();
        
        // 测试API密钥输入
        await page.locator('#api-key').fill(TEST_CONFIG.mockApiKey);
        await expect(page.locator('#api-key')).toHaveValue(TEST_CONFIG.mockApiKey);
        
        // 测试显示/隐藏API密钥
        await page.locator('#toggle-api-key').click();
        const inputType = await page.locator('#api-key').getAttribute('type');
        expect(inputType).toBe('text');
        
        await page.close();
    });

    test('4. 内容脚本注入测试', async () => {
        test.setTimeout(TEST_CONFIG.timeout);
        
        const page = await browser.newPage();
        await page.goto(TEST_CONFIG.testPageUrl);
        
        // 等待页面加载完成
        await page.waitForLoadState('networkidle');
        
        // 检查内容脚本是否注入
        const contentScriptLoaded = await page.evaluate(() => {
            return typeof window.chrome !== 'undefined' && 
                   typeof chrome.runtime !== 'undefined';
        });
        
        expect(contentScriptLoaded).toBe(true);
        
        await page.close();
    });

    test('5. 本地Mermaid渲染器测试', async () => {
        test.setTimeout(TEST_CONFIG.timeout);
        
        const page = await browser.newPage();
        const testRendererUrl = `chrome-extension://${extensionId}/test-local-renderer.html`;
        await page.goto(testRendererUrl);
        
        // 等待渲染器加载
        await page.waitForFunction(() => {
            return typeof window.renderMermaidLocally === 'function';
        });
        
        // 测试渲染功能
        await page.locator('#renderTest').click();
        
        // 验证SVG生成
        await page.waitForSelector('#testOutput svg', { timeout: 5000 });
        const svgElement = await page.locator('#testOutput svg');
        await expect(svgElement).toBeVisible();
        
        await page.close();
    });

    test('6. 权限和安全测试', async () => {
        test.setTimeout(TEST_CONFIG.timeout);
        
        const page = await browser.newPage();
        
        // 测试在排除网站上的行为
        await page.goto('https://accounts.google.com');
        
        // 验证内容脚本不应在排除的网站上运行
        const hasContentScript = await page.evaluate(() => {
            return document.querySelector('script[src*="content.js"]') !== null;
        });
        
        expect(hasContentScript).toBe(false);
        
        await page.close();
    });

    test('7. 错误处理测试', async () => {
        test.setTimeout(TEST_CONFIG.timeout);
        
        const page = await browser.newPage();
        const popupUrl = `chrome-extension://${extensionId}/popup.html`;
        await page.goto(popupUrl);
        
        // 测试没有API密钥时的行为
        await page.locator('#summarize-btn').click();
        
        // 验证错误提示
        await expect(page.locator('#status-message')).toContainText('请设置Gemini API密钥');
        
        await page.close();
    });

    test('8. 响应式设计测试', async () => {
        test.setTimeout(TEST_CONFIG.timeout);
        
        const page = await browser.newPage();
        const popupUrl = `chrome-extension://${extensionId}/popup.html`;
        await page.goto(popupUrl);
        
        // 测试不同视口大小
        await page.setViewportSize({ width: 320, height: 568 }); // 移动设备
        await expect(page.locator('.container')).toBeVisible();
        
        await page.setViewportSize({ width: 1024, height: 768 }); // 桌面设备
        await expect(page.locator('.container')).toBeVisible();
        
        await page.close();
    });

    test('9. 性能测试', async () => {
        test.setTimeout(TEST_CONFIG.timeout);
        
        const page = await browser.newPage();
        
        // 开始性能监控
        await page.coverage.startJSCoverage();
        const startTime = Date.now();
        
        // 加载弹出窗口
        const popupUrl = `chrome-extension://${extensionId}/popup.html`;
        await page.goto(popupUrl);
        
        // 等待完全加载
        await page.waitForLoadState('networkidle');
        const loadTime = Date.now() - startTime;
        
        // 验证加载时间合理（小于3秒）
        expect(loadTime).toBeLessThan(3000);
        
        // 获取JS覆盖率
        const jsCoverage = await page.coverage.stopJSCoverage();
        console.log('JS文件数量:', jsCoverage.length);
        
        await page.close();
    });

    test('10. 数据存储测试', async () => {
        test.setTimeout(TEST_CONFIG.timeout);
        
        const page = await browser.newPage();
        const optionsUrl = `chrome-extension://${extensionId}/options.html`;
        await page.goto(optionsUrl);
        
        // 设置API密钥
        await page.locator('#api-key').fill(TEST_CONFIG.mockApiKey);
        await page.locator('#save-settings').click();
        
        // 验证保存成功提示
        await expect(page.locator('.status-message')).toContainText('设置已保存');
        
        // 刷新页面验证数据持久化
        await page.reload();
        await expect(page.locator('#api-key')).toHaveValue(TEST_CONFIG.mockApiKey);
        
        await page.close();
    });
});

// 导出测试配置供其他测试文件使用
module.exports = {
    TEST_CONFIG,
    launchBrowserWithExtension,
    getExtensionId
};
