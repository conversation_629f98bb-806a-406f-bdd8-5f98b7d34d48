/**
 * Comprehensive test script for Gemini Web Summarizer Extension
 * Tests flowchart generation functionality using Playwright
 */

const { chromium } = require('playwright');
const path = require('path');

async function testExtension() {
    console.log('🚀 Starting Gemini Web Summarizer Extension Tests...');
    
    // Launch browser with extension
    const browser = await chromium.launchPersistentContext('', {
        headless: false,
        args: [
            `--load-extension=${path.resolve(__dirname)}`,
            '--disable-extensions-except=' + path.resolve(__dirname),
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
        ]
    });
    
    const page = await browser.newPage();
    
    try {
        // Test 1: Load test page
        console.log('📄 Loading test page...');
        await page.goto(`file://${path.resolve(__dirname, 'test-flowchart-generation.html')}`);
        await page.waitForLoadState('networkidle');
        
        // Test 2: Open extension popup
        console.log('🔧 Opening extension popup...');
        
        // Find extension ID (it will be auto-generated)
        const extensionId = await getExtensionId(page);
        console.log('Extension ID:', extensionId);
        
        // Open extension popup
        const popupPage = await browser.newPage();
        await popupPage.goto(`chrome-extension://${extensionId}/popup.html`);
        await popupPage.waitForLoadState('networkidle');
        
        // Test 3: Check if extension loaded correctly
        console.log('✅ Checking extension initialization...');
        const initStatus = await popupPage.evaluate(() => {
            return {
                hasElements: !!document.getElementById('settings-btn'),
                hasFlowchartTab: !!document.querySelector('[data-tab="flowchart"]'),
                hasGenerateBtn: !!document.getElementById('generate-flowchart-btn'),
                consoleMessages: window.console ? 'Console available' : 'No console'
            };
        });
        console.log('Extension status:', initStatus);
        
        // Test 4: Switch to flowchart tab
        console.log('📊 Switching to flowchart tab...');
        await popupPage.click('[data-tab="flowchart"]');
        await popupPage.waitForTimeout(500);
        
        // Verify tab switch
        const isFlowchartActive = await popupPage.evaluate(() => {
            const flowchartTab = document.querySelector('[data-tab="flowchart"]');
            return flowchartTab && flowchartTab.classList.contains('active');
        });
        console.log('Flowchart tab active:', isFlowchartActive);
        
        // Test 5: Set up API key (mock for testing)
        console.log('🔑 Setting up API key...');
        await popupPage.evaluate(() => {
            // Set a test API key in storage
            chrome.storage.sync.set({
                geminiApiKey: 'test-api-key-for-testing',
                defaultModel: 'gemini-2.5-flash-lite'
            });
        });
        
        // Test 6: Test flowchart generation (with mock)
        console.log('🎯 Testing flowchart generation...');
        
        // Mock the API call for testing
        await popupPage.evaluate(() => {
            // Override the API call function for testing
            window.originalCallGeminiAPI = window.callGeminiAPI;
            window.callGeminiAPI = async function(apiKey, prompt) {
                console.log('Mock API call with prompt:', prompt.substring(0, 100) + '...');
                
                // Return a mock Mermaid flowchart
                return `flowchart TD
    A[开始] --> B[用户访问注册页面]
    B --> C[填写注册表单]
    C --> D{数据验证}
    D -->|有效| E[创建用户账户]
    D -->|无效| F[显示错误信息]
    E --> G[发送验证邮件]
    F --> C
    G --> H[用户点击验证链接]
    H --> I{链接有效?}
    I -->|是| J[激活账户]
    I -->|否| K[显示错误页面]
    J --> L[跳转到登录页面]
    K --> M[结束]
    L --> M`;
            };
        });
        
        // Click generate flowchart button
        await popupPage.click('#generate-flowchart-btn');
        
        // Wait for generation to complete
        await popupPage.waitForTimeout(3000);
        
        // Test 7: Verify flowchart was generated
        console.log('🔍 Verifying flowchart generation...');
        const flowchartResult = await popupPage.evaluate(() => {
            const mermaidChart = document.getElementById('mermaid-chart');
            const container = mermaidChart?.querySelector('.mermaid-container');
            const svg = mermaidChart?.querySelector('svg');
            
            return {
                hasContainer: !!container,
                hasSVG: !!svg,
                svgContent: svg ? svg.outerHTML.substring(0, 200) + '...' : 'No SVG',
                containerStyle: container ? container.style.cssText : 'No container',
                chartHTML: mermaidChart ? mermaidChart.innerHTML.substring(0, 300) + '...' : 'No chart'
            };
        });
        console.log('Flowchart result:', flowchartResult);
        
        // Test 8: Test interactive features
        if (flowchartResult.hasContainer) {
            console.log('🎮 Testing interactive features...');
            
            // Test zoom functionality
            await popupPage.click('#zoom-in-btn');
            await popupPage.waitForTimeout(500);
            
            await popupPage.click('#zoom-out-btn');
            await popupPage.waitForTimeout(500);
            
            // Test reset
            await popupPage.click('#reset-zoom-btn');
            await popupPage.waitForTimeout(500);
            
            // Test mouse wheel zoom (simulate)
            await popupPage.evaluate(() => {
                const container = document.querySelector('.mermaid-container');
                if (container) {
                    const wheelEvent = new WheelEvent('wheel', {
                        deltaY: -100,
                        clientX: 200,
                        clientY: 200
                    });
                    container.dispatchEvent(wheelEvent);
                }
            });
            
            console.log('✅ Interactive features tested');
        }
        
        // Test 9: Test error handling
        console.log('⚠️ Testing error handling...');
        await popupPage.evaluate(() => {
            // Restore original API function and make it fail
            window.callGeminiAPI = async function() {
                throw new Error('Test API error');
            };
        });
        
        // Try to generate flowchart again
        await popupPage.click('#generate-flowchart-btn');
        await popupPage.waitForTimeout(2000);
        
        // Check error handling
        const errorResult = await popupPage.evaluate(() => {
            const statusMessage = document.getElementById('status-message');
            const mermaidChart = document.getElementById('mermaid-chart');
            
            return {
                statusText: statusMessage ? statusMessage.textContent : 'No status',
                hasErrorDisplay: mermaidChart ? mermaidChart.innerHTML.includes('流程图渲染失败') : false
            };
        });
        console.log('Error handling result:', errorResult);
        
        // Test 10: Test model selection
        console.log('🔧 Testing model selection...');
        const modelOptions = await popupPage.evaluate(() => {
            const modelSelect = document.getElementById('quick-model-select');
            if (!modelSelect) return { error: 'No model select found' };
            
            const options = Array.from(modelSelect.options).map(opt => ({
                value: opt.value,
                text: opt.textContent
            }));
            
            return { options, selectedValue: modelSelect.value };
        });
        console.log('Model selection:', modelOptions);
        
        // Verify only Flash variants are available
        const hasOnlyFlashModels = modelOptions.options && 
            modelOptions.options.every(opt => opt.value.includes('flash'));
        console.log('Has only Flash models:', hasOnlyFlashModels);
        
        // Test Summary
        console.log('\n📋 TEST SUMMARY:');
        console.log('================');
        console.log('✅ Extension loaded:', initStatus.hasElements);
        console.log('✅ Flowchart tab working:', isFlowchartActive);
        console.log('✅ Flowchart generation:', flowchartResult.hasSVG);
        console.log('✅ Interactive features:', flowchartResult.hasContainer);
        console.log('✅ Error handling:', errorResult.hasErrorDisplay);
        console.log('✅ Model selection simplified:', hasOnlyFlashModels);
        
        console.log('\n🎉 All tests completed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        // Keep browser open for manual inspection
        console.log('\n🔍 Browser kept open for manual inspection...');
        console.log('Press Ctrl+C to close when done.');
    }
}

async function getExtensionId(page) {
    // Navigate to chrome://extensions to find the extension ID
    await page.goto('chrome://extensions/');
    await page.waitForTimeout(1000);
    
    // Enable developer mode if not already enabled
    const devModeToggle = page.locator('#devMode');
    const isEnabled = await devModeToggle.isChecked();
    if (!isEnabled) {
        await devModeToggle.click();
        await page.waitForTimeout(500);
    }
    
    // Find the extension ID
    const extensionId = await page.evaluate(() => {
        const extensions = document.querySelectorAll('extensions-item');
        for (const ext of extensions) {
            const name = ext.shadowRoot?.querySelector('#name')?.textContent;
            if (name && name.includes('Gemini Web Summarizer')) {
                return ext.getAttribute('id');
            }
        }
        return null;
    });
    
    return extensionId;
}

// Run the tests
if (require.main === module) {
    testExtension().catch(console.error);
}

module.exports = { testExtension };
