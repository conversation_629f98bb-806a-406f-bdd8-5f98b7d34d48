<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成扩展图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }
        .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: transform 0.2s;
        }
        .icon:hover {
            transform: scale(1.05);
        }
        .icon-16 {
            width: 16px;
            height: 16px;
            font-size: 8px;
        }
        .icon-48 {
            width: 48px;
            height: 48px;
            font-size: 24px;
        }
        .icon-128 {
            width: 128px;
            height: 128px;
            font-size: 64px;
        }
        .instructions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px;
        }
    </style>
</head>
<body>
    <h1>🤖 Gemini Web Summarizer 图标生成器</h1>
    
    <div class="instructions">
        <h2>使用说明</h2>
        <p>1. 点击下方的"生成图标"按钮</p>
        <p>2. 右键点击生成的图标，选择"图片另存为"</p>
        <p>3. 将图标保存到 <code>icons/</code> 文件夹中，文件名分别为：</p>
        <ul>
            <li><code>icon16.png</code> - 16x16像素</li>
            <li><code>icon48.png</code> - 48x48像素</li>
            <li><code>icon128.png</code> - 128x128像素</li>
        </ul>
    </div>
    
    <div class="icon-container">
        <div class="icon icon-16">🤖</div>
        <div class="icon icon-48">🤖</div>
        <div class="icon icon-128">🤖</div>
    </div>
    
    <button class="download-btn" onclick="generateIcons()">生成图标</button>
    <button class="download-btn" onclick="downloadAllIcons()" style="background: #28a745;">一键下载所有图标</button>

    <div id="canvas-container"></div>
    
    <script>
        function generateIcons() {
            const container = document.getElementById('canvas-container');
            container.innerHTML = '';
            
            const sizes = [
                { size: 16, fontSize: 10 },
                { size: 48, fontSize: 30 },
                { size: 128, fontSize: 80 }
            ];
            
            sizes.forEach(({ size, fontSize }) => {
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                const ctx = canvas.getContext('2d');
                
                // 创建渐变背景
                const gradient = ctx.createLinearGradient(0, 0, size, size);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                
                // 绘制背景
                ctx.fillStyle = gradient;
                ctx.roundRect(0, 0, size, size, size * 0.15);
                ctx.fill();
                
                // 绘制机器人图标
                ctx.fillStyle = 'white';
                ctx.font = `${fontSize}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('🤖', size / 2, size / 2);
                
                // 添加标签
                const label = document.createElement('p');
                label.textContent = `${size}x${size} - 右键保存为 icon${size}.png`;
                label.style.textAlign = 'center';
                label.style.margin = '5px';
                
                container.appendChild(canvas);
                container.appendChild(label);
            });
        }
        
        // 一键下载所有图标
        function downloadAllIcons() {
            const sizes = [16, 48, 128];
            sizes.forEach((size, index) => {
                setTimeout(() => {
                    const canvas = createIconCanvas(size);
                    downloadCanvas(canvas, `icon${size}.png`);
                }, index * 500); // 延迟下载避免浏览器阻止
            });
        }

        // 创建图标画布
        function createIconCanvas(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');

            // 绘制背景
            ctx.fillStyle = gradient;
            ctx.roundRect(0, 0, size, size, size * 0.15);
            ctx.fill();

            // 绘制机器人图标 - 使用简单的几何形状代替emoji
            ctx.fillStyle = 'white';

            // 绘制机器人头部
            const headSize = size * 0.4;
            const headX = (size - headSize) / 2;
            const headY = size * 0.25;
            ctx.fillRect(headX, headY, headSize, headSize);

            // 绘制眼睛
            ctx.fillStyle = '#667eea';
            const eyeSize = size * 0.08;
            const eyeY = headY + headSize * 0.3;
            ctx.fillRect(headX + headSize * 0.25, eyeY, eyeSize, eyeSize);
            ctx.fillRect(headX + headSize * 0.65, eyeY, eyeSize, eyeSize);

            // 绘制嘴巴
            const mouthWidth = headSize * 0.4;
            const mouthHeight = size * 0.03;
            const mouthX = headX + (headSize - mouthWidth) / 2;
            const mouthY = headY + headSize * 0.7;
            ctx.fillRect(mouthX, mouthY, mouthWidth, mouthHeight);

            return canvas;
        }

        // 下载画布为PNG
        function downloadCanvas(canvas, filename) {
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }

        // 添加圆角矩形支持
        CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
            this.beginPath();
            this.moveTo(x + radius, y);
            this.lineTo(x + width - radius, y);
            this.quadraticCurveTo(x + width, y, x + width, y + radius);
            this.lineTo(x + width, y + height - radius);
            this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            this.lineTo(x + radius, y + height);
            this.quadraticCurveTo(x, y + height, x, y + height - radius);
            this.lineTo(x, y + radius);
            this.quadraticCurveTo(x, y, x + radius, y);
            this.closePath();
        };
    </script>
</body>
</html>
