/**
 * Playwright全局设置 - 测试前准备
 */

const fs = require('fs');
const path = require('path');

async function globalSetup(config) {
    console.log('🚀 开始Gemini Web Summarizer扩展测试准备...');
    
    // 创建测试结果目录
    const testResultsDir = path.join(__dirname, 'test-results');
    if (!fs.existsSync(testResultsDir)) {
        fs.mkdirSync(testResultsDir, { recursive: true });
    }
    
    // 验证扩展文件存在
    const extensionPath = path.resolve(__dirname, '..');
    const manifestPath = path.join(extensionPath, 'manifest.json');
    
    if (!fs.existsSync(manifestPath)) {
        throw new Error('❌ 找不到manifest.json文件，请确保扩展文件完整');
    }
    
    // 验证manifest.json格式
    try {
        const manifestContent = fs.readFileSync(manifestPath, 'utf8');
        JSON.parse(manifestContent);
        console.log('✅ manifest.json格式验证通过');
    } catch (error) {
        throw new Error(`❌ manifest.json格式错误: ${error.message}`);
    }
    
    // 验证关键文件存在
    const requiredFiles = [
        'popup.html',
        'popup-fixed.js',
        'options.html',
        'options.js',
        'background.js',
        'content.js',
        'lib/mermaid-renderer.js'
    ];
    
    for (const file of requiredFiles) {
        const filePath = path.join(extensionPath, file);
        if (!fs.existsSync(filePath)) {
            console.warn(`⚠️ 警告: 文件不存在 ${file}`);
        } else {
            console.log(`✅ 文件存在: ${file}`);
        }
    }
    
    // 创建测试报告目录
    const reportDirs = ['html-report', 'screenshots', 'videos', 'traces'];
    for (const dir of reportDirs) {
        const dirPath = path.join(testResultsDir, dir);
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }
    
    console.log('✅ 测试环境准备完成');
    
    // 返回配置信息供测试使用
    return {
        extensionPath,
        testResultsDir
    };
}

module.exports = globalSetup;
