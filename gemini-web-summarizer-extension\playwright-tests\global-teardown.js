/**
 * Playwright全局清理 - 测试后清理
 */

const fs = require('fs');
const path = require('path');

async function globalTeardown(config) {
    console.log('🧹 开始测试清理...');
    
    const testResultsDir = path.join(__dirname, 'test-results');
    
    // 生成测试摘要
    try {
        const resultsFile = path.join(testResultsDir, 'results.json');
        if (fs.existsSync(resultsFile)) {
            const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'));
            
            console.log('\n📊 测试结果摘要:');
            console.log(`总测试数: ${results.stats.total}`);
            console.log(`通过: ${results.stats.passed}`);
            console.log(`失败: ${results.stats.failed}`);
            console.log(`跳过: ${results.stats.skipped}`);
            console.log(`用时: ${Math.round(results.stats.duration / 1000)}秒`);
            
            if (results.stats.failed > 0) {
                console.log('\n❌ 失败的测试:');
                results.suites.forEach(suite => {
                    suite.specs.forEach(spec => {
                        spec.tests.forEach(test => {
                            if (test.results.some(r => r.status === 'failed')) {
                                console.log(`  - ${spec.title}: ${test.title}`);
                            }
                        });
                    });
                });
            }
        }
    } catch (error) {
        console.log('⚠️ 无法读取测试结果文件');
    }
    
    // 清理临时文件（可选）
    // 注意：通常我们保留测试结果用于分析
    
    console.log('✅ 测试清理完成');
    console.log(`📁 测试结果保存在: ${testResultsDir}`);
}

module.exports = globalTeardown;
