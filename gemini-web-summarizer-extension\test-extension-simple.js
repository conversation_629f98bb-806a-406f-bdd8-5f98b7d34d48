/**
 * 简化的扩展测试 - 使用Playwright MCP服务
 */

const { chromium } = require('playwright');
const path = require('path');

// 扩展路径
const EXTENSION_PATH = path.resolve(__dirname);

async function testExtension() {
    console.log('🚀 开始测试 Gemini Web Summarizer 扩展...');
    
    let browser;
    try {
        // 启动带扩展的浏览器
        browser = await chromium.launchPersistentContext('', {
            headless: false,
            args: [
                `--disable-extensions-except=${EXTENSION_PATH}`,
                `--load-extension=${EXTENSION_PATH}`,
                '--no-sandbox'
            ],
            viewport: { width: 1280, height: 720 }
        });
        
        console.log('✅ 浏览器启动成功');
        
        // 测试1: 验证扩展加载
        console.log('\n📋 测试1: 扩展加载验证');
        const page = await browser.newPage();
        await page.goto('chrome://extensions/');
        
        // 启用开发者模式
        await page.locator('#devMode').check();
        
        // 查找扩展
        const extensionCards = await page.locator('extensions-item').all();
        let extensionFound = false;
        let extensionId = null;
        
        for (const card of extensionCards) {
            const name = await card.locator('#name').textContent();
            if (name && name.includes('Gemini Web Summarizer')) {
                extensionFound = true;
                extensionId = await card.getAttribute('id');
                console.log('✅ 扩展已加载，ID:', extensionId);
                break;
            }
        }
        
        if (!extensionFound) {
            throw new Error('❌ 扩展未找到');
        }
        
        // 测试2: 弹出窗口UI测试
        console.log('\n📋 测试2: 弹出窗口UI测试');
        const popupUrl = `chrome-extension://${extensionId}/popup.html`;
        await page.goto(popupUrl);
        
        // 验证基本UI元素
        const title = await page.locator('h1').textContent();
        console.log('页面标题:', title);
        
        const summarizeBtn = page.locator('#summarize-btn');
        const flowchartBtn = page.locator('#generate-flowchart-btn');
        
        if (await summarizeBtn.isVisible()) {
            console.log('✅ 总结按钮可见');
        } else {
            console.log('❌ 总结按钮不可见');
        }
        
        if (await flowchartBtn.isVisible()) {
            console.log('✅ 流程图按钮可见');
        } else {
            console.log('❌ 流程图按钮不可见');
        }
        
        // 测试标签页切换
        const summaryTab = page.locator('#summary-tab');
        const flowchartTab = page.locator('#flowchart-tab');
        
        if (await summaryTab.isVisible()) {
            await summaryTab.click();
            console.log('✅ 总结标签页切换成功');
        }
        
        if (await flowchartTab.isVisible()) {
            await flowchartTab.click();
            console.log('✅ 流程图标签页切换成功');
        }
        
        // 测试3: 设置页面测试
        console.log('\n📋 测试3: 设置页面测试');
        const optionsUrl = `chrome-extension://${extensionId}/options.html`;
        await page.goto(optionsUrl);
        
        const apiKeyInput = page.locator('#api-key');
        const saveBtn = page.locator('#save-settings');
        
        if (await apiKeyInput.isVisible()) {
            console.log('✅ API密钥输入框可见');
            
            // 测试输入
            await apiKeyInput.fill('test-api-key');
            const inputValue = await apiKeyInput.inputValue();
            if (inputValue === 'test-api-key') {
                console.log('✅ API密钥输入功能正常');
            }
        }
        
        if (await saveBtn.isVisible()) {
            console.log('✅ 保存按钮可见');
        }
        
        // 测试4: 本地Mermaid渲染器测试
        console.log('\n📋 测试4: 本地Mermaid渲染器测试');
        const testRendererUrl = `chrome-extension://${extensionId}/test-local-renderer.html`;
        
        try {
            await page.goto(testRendererUrl);
            
            // 等待渲染器加载
            await page.waitForFunction(() => {
                return typeof window.renderMermaidLocally === 'function';
            }, { timeout: 5000 });
            
            console.log('✅ 本地Mermaid渲染器加载成功');
            
            // 测试渲染功能
            const renderBtn = page.locator('button:has-text("渲染测试")');
            if (await renderBtn.isVisible()) {
                await renderBtn.click();
                
                // 等待SVG生成
                try {
                    await page.waitForSelector('svg', { timeout: 5000 });
                    console.log('✅ Mermaid图表渲染成功');
                } catch (e) {
                    console.log('⚠️ Mermaid图表渲染超时');
                }
            }
        } catch (e) {
            console.log('⚠️ 测试渲染器页面不存在或加载失败');
        }
        
        // 测试5: 错误处理测试
        console.log('\n📋 测试5: 错误处理测试');
        await page.goto(popupUrl);
        
        // 测试没有API密钥时的行为
        await page.locator('#summarize-btn').click();
        
        // 等待状态消息
        await page.waitForTimeout(1000);
        const statusMessage = await page.locator('#status-message').textContent();
        if (statusMessage && statusMessage.includes('API密钥')) {
            console.log('✅ 错误处理正常:', statusMessage);
        } else {
            console.log('⚠️ 错误处理可能有问题');
        }
        
        console.log('\n🎉 所有测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    } finally {
        if (browser) {
            await browser.close();
            console.log('🔚 浏览器已关闭');
        }
    }
}

// 运行测试
testExtension();
