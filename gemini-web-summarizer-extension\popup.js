// 全局变量
let currentZoom = 1;
let isDragging = false;
let dragStart = { x: 0, y: 0 };
let chartOffset = { x: 0, y: 0 };

// DOM元素
const elements = {
    tabButtons: document.querySelectorAll('.tab-button'),
    tabPanes: document.querySelectorAll('.tab-pane'),
    summarizeBtn: document.getElementById('summarize-btn'),
    generateFlowchartBtn: document.getElementById('generate-flowchart-btn'),
    copySummaryBtn: document.getElementById('copy-summary-btn'),
    settingsBtn: document.getElementById('settings-btn'),
    openSettingsBtn: document.getElementById('open-settings-btn'),
    summaryResult: document.getElementById('summary-result'),
    flowchartResult: document.getElementById('flowchart-result'),
    summaryContent: document.getElementById('summary-content'),
    mermaidChart: document.getElementById('mermaid-chart'),
    statusMessage: document.getElementById('status-message'),
    apiKeyPrompt: document.getElementById('api-key-prompt'),
    zoomInBtn: document.getElementById('zoom-in-btn'),
    zoomOutBtn: document.getElementById('zoom-out-btn'),
    resetZoomBtn: document.getElementById('reset-zoom-btn'),
    downloadPngBtn: document.getElementById('download-png-btn'),
    downloadSvgBtn: document.getElementById('download-svg-btn')
};

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    await initializeExtension();
    setupEventListeners();
    checkApiKey();
});

// 初始化扩展
async function initializeExtension() {
    updateStatus('扩展已加载');

    // 加载Mermaid库
    try {
        await loadMermaid();
        updateStatus('图表库已加载');
    } catch (error) {
        console.error('加载图表库失败:', error);
        updateStatus('图表库加载失败，流程图功能可能不可用');
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 标签页切换
    elements.tabButtons.forEach(button => {
        button.addEventListener('click', () => switchTab(button.dataset.tab));
    });
    
    // 主要功能按钮
    elements.summarizeBtn.addEventListener('click', handleSummarize);
    elements.generateFlowchartBtn.addEventListener('click', handleGenerateFlowchart);
    elements.copySummaryBtn.addEventListener('click', handleCopySummary);
    
    // 设置按钮
    elements.settingsBtn.addEventListener('click', openSettings);
    elements.openSettingsBtn.addEventListener('click', openSettings);
    
    // 图表控制按钮
    elements.zoomInBtn.addEventListener('click', () => zoomChart(1.2));
    elements.zoomOutBtn.addEventListener('click', () => zoomChart(0.8));
    elements.resetZoomBtn.addEventListener('click', resetChart);
    elements.downloadPngBtn.addEventListener('click', () => downloadChart('png'));
    elements.downloadSvgBtn.addEventListener('click', () => downloadChart('svg'));
    
    // 图表拖拽功能
    setupChartDragging();
}

// 切换标签页
function switchTab(tabName) {
    elements.tabButtons.forEach(btn => btn.classList.remove('active'));
    elements.tabPanes.forEach(pane => pane.classList.remove('active'));
    
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

// 检查API密钥
async function checkApiKey() {
    try {
        const result = await chrome.storage.sync.get(['geminiApiKey']);
        if (!result.geminiApiKey) {
            elements.apiKeyPrompt.style.display = 'flex';
            updateStatus('请设置Gemini API密钥');
            return false;
        }
        elements.apiKeyPrompt.style.display = 'none';
        return true;
    } catch (error) {
        console.error('检查API密钥失败:', error);
        updateStatus('检查API密钥失败');
        return false;
    }
}

// 处理总结功能
async function handleSummarize() {
    if (!(await checkApiKey())) return;
    
    setButtonLoading(elements.summarizeBtn, true);
    updateStatus('正在获取页面内容...');
    
    try {
        // 获取当前标签页内容
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        const pageContent = await getPageContent(tab.id);
        
        if (!pageContent) {
            throw new Error('无法获取页面内容');
        }
        
        updateStatus('正在生成总结...');
        
        // 调用Gemini API生成总结
        const summary = await generateSummary(pageContent);
        
        // 显示总结结果
        elements.summaryContent.innerHTML = formatSummary(summary);
        elements.summaryResult.style.display = 'block';
        
        updateStatus('总结完成');
    } catch (error) {
        console.error('总结失败:', error);
        updateStatus(`总结失败: ${error.message}`);
        showError('总结失败，请检查网络连接和API密钥设置');
    } finally {
        setButtonLoading(elements.summarizeBtn, false);
    }
}

// 处理流程图生成
async function handleGenerateFlowchart() {
    if (!(await checkApiKey())) return;
    
    setButtonLoading(elements.generateFlowchartBtn, true);
    updateStatus('正在获取页面内容...');
    
    try {
        // 获取当前标签页内容
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        const pageContent = await getPageContent(tab.id);
        
        if (!pageContent) {
            throw new Error('无法获取页面内容');
        }
        
        updateStatus('正在生成流程图...');
        
        // 调用Gemini API生成Mermaid代码
        const mermaidCode = await generateFlowchart(pageContent);
        
        // 渲染Mermaid图表
        await renderChart(mermaidCode);
        
        elements.flowchartResult.style.display = 'block';
        updateStatus('流程图生成完成');
    } catch (error) {
        console.error('流程图生成失败:', error);
        updateStatus(`流程图生成失败: ${error.message}`);
        showError('流程图生成失败，请检查网络连接和API密钥设置');
    } finally {
        setButtonLoading(elements.generateFlowchartBtn, false);
    }
}

// 获取页面内容
async function getPageContent(tabId) {
    try {
        const results = await chrome.scripting.executeScript({
            target: { tabId },
            function: extractPageContent
        });
        return results[0]?.result;
    } catch (error) {
        console.error('获取页面内容失败:', error);
        throw new Error('无法访问页面内容，请刷新页面后重试');
    }
}

// 提取页面内容的函数（在页面上下文中执行）
function extractPageContent() {
    // 移除脚本和样式标签
    const scripts = document.querySelectorAll('script, style, noscript');
    scripts.forEach(el => el.remove());
    
    // 获取主要内容
    const content = document.body.innerText || document.body.textContent || '';
    
    // 清理和限制内容长度
    const cleanContent = content
        .replace(/\s+/g, ' ')
        .replace(/\n+/g, '\n')
        .trim()
        .substring(0, 8000); // 限制长度避免API调用过大
    
    return {
        title: document.title,
        url: window.location.href,
        content: cleanContent
    };
}

// 生成总结
async function generateSummary(pageData) {
    const { geminiApiKey } = await chrome.storage.sync.get(['geminiApiKey']);
    
    const prompt = `请对以下网页内容进行智能总结，要求：
1. 提取核心要点和关键信息
2. 保持逻辑清晰，结构化呈现
3. 总结长度控制在300-500字
4. 使用中文回复

网页标题：${pageData.title}
网页URL：${pageData.url}
网页内容：${pageData.content}`;

    return await callGeminiAPI(geminiApiKey, prompt);
}

// 生成流程图
async function generateFlowchart(pageData) {
    const { geminiApiKey } = await chrome.storage.sync.get(['geminiApiKey']);
    
    const prompt = `基于以下网页内容，生成一个Mermaid格式的流程图，要求：
1. 提取主要流程和步骤
2. 使用flowchart TD语法
3. 节点名称使用中文
4. 只返回Mermaid代码，不要其他解释文字
5. 确保语法正确可渲染

网页标题：${pageData.title}
网页内容：${pageData.content}

请直接返回Mermaid代码：`;

    const response = await callGeminiAPI(geminiApiKey, prompt);
    
    // 提取Mermaid代码
    const mermaidMatch = response.match(/```mermaid\n([\s\S]*?)\n```/) || 
                        response.match(/```\n([\s\S]*?)\n```/) ||
                        [null, response];
    
    return mermaidMatch[1] || response;
}

// 复制总结内容
async function handleCopySummary() {
    try {
        const summaryText = elements.summaryContent.innerText;
        await navigator.clipboard.writeText(summaryText);
        updateStatus('总结内容已复制到剪贴板');
        
        // 临时改变按钮文本
        const originalText = elements.copySummaryBtn.textContent;
        elements.copySummaryBtn.textContent = '✅';
        setTimeout(() => {
            elements.copySummaryBtn.textContent = originalText;
        }, 1000);
    } catch (error) {
        console.error('复制失败:', error);
        updateStatus('复制失败');
    }
}

// 设置按钮加载状态
function setButtonLoading(button, isLoading) {
    const textSpan = button.querySelector('.btn-text');
    const spinner = button.querySelector('.loading-spinner');
    
    if (isLoading) {
        textSpan.style.display = 'none';
        spinner.style.display = 'inline';
        button.disabled = true;
    } else {
        textSpan.style.display = 'inline';
        spinner.style.display = 'none';
        button.disabled = false;
    }
}

// 更新状态消息
function updateStatus(message) {
    elements.statusMessage.textContent = message;
}

// 显示错误消息
function showError(message) {
    elements.statusMessage.textContent = message;
    elements.statusMessage.style.color = '#dc3545';
    setTimeout(() => {
        elements.statusMessage.style.color = '#6c757d';
    }, 5000);
}

// 格式化总结内容
function formatSummary(summary) {
    return summary
        .replace(/\n/g, '<br>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>');
}

// 打开设置页面
function openSettings() {
    chrome.runtime.openOptionsPage();
}

// 重试渲染图表
function retryRenderChart() {
    const lastMermaidCode = elements.mermaidChart.dataset.lastCode;
    if (lastMermaidCode) {
        renderChart(lastMermaidCode);
    }
}

// 渲染Mermaid图表
async function renderChart(mermaidCode) {
    try {
        elements.mermaidChart.innerHTML = '';

        // 保存代码以便重试
        elements.mermaidChart.dataset.lastCode = mermaidCode;

        // 使用全局的渲染函数
        await window.renderMermaidChart(mermaidCode, 'mermaid-chart');

        // 重置图表状态
        currentZoom = 1;
        chartOffset = { x: 0, y: 0 };
        updateChartTransform();

    } catch (error) {
        console.error('Mermaid渲染失败:', error);
        elements.mermaidChart.innerHTML = `
            <div style="padding: 20px; text-align: center; color: #dc3545;">
                <p>流程图渲染失败</p>
                <p style="font-size: 12px; margin-top: 8px;">请检查生成的Mermaid代码格式</p>
                <div style="margin-top: 10px;">
                    <button onclick="retryRenderChart()" style="padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">重试</button>
                </div>
            </div>
        `;
        throw new Error('流程图渲染失败');
    }
}

// 图表缩放功能
function zoomChart(factor) {
    currentZoom *= factor;
    currentZoom = Math.max(0.5, Math.min(3, currentZoom)); // 限制缩放范围
    updateChartTransform();
}

// 重置图表
function resetChart() {
    currentZoom = 1;
    chartOffset = { x: 0, y: 0 };
    updateChartTransform();
}

// 更新图表变换
function updateChartTransform() {
    const svg = elements.mermaidChart.querySelector('svg');
    if (svg) {
        svg.style.transform = `translate(${chartOffset.x}px, ${chartOffset.y}px) scale(${currentZoom})`;
        svg.style.transformOrigin = 'center center';
    }
}

// 设置图表拖拽功能
function setupChartDragging() {
    elements.mermaidChart.addEventListener('mousedown', (e) => {
        isDragging = true;
        dragStart.x = e.clientX - chartOffset.x;
        dragStart.y = e.clientY - chartOffset.y;
        elements.mermaidChart.style.cursor = 'grabbing';
    });

    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;

        chartOffset.x = e.clientX - dragStart.x;
        chartOffset.y = e.clientY - dragStart.y;
        updateChartTransform();
    });

    document.addEventListener('mouseup', () => {
        isDragging = false;
        elements.mermaidChart.style.cursor = 'grab';
    });

    // 鼠标滚轮缩放
    elements.mermaidChart.addEventListener('wheel', (e) => {
        e.preventDefault();
        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
        zoomChart(zoomFactor);
    });
}

// 下载图表
async function downloadChart(format) {
    try {
        const svg = elements.mermaidChart.querySelector('svg');
        if (!svg) {
            throw new Error('没有可下载的图表');
        }

        if (format === 'svg') {
            downloadSVG(svg);
        } else if (format === 'png') {
            await downloadPNG(svg);
        }

        updateStatus(`图表已下载为${format.toUpperCase()}格式`);
    } catch (error) {
        console.error('下载失败:', error);
        updateStatus('下载失败');
    }
}

// 下载SVG
function downloadSVG(svg) {
    const svgData = new XMLSerializer().serializeToString(svg);
    const blob = new Blob([svgData], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'flowchart.svg';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 下载PNG
async function downloadPNG(svg) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    return new Promise((resolve, reject) => {
        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);

            canvas.toBlob((blob) => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'flowchart.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                resolve();
            });
        };

        img.onerror = reject;

        const svgData = new XMLSerializer().serializeToString(svg);
        const blob = new Blob([svgData], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        img.src = url;
    });
}
