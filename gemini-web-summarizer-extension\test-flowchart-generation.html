<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flowchart Generation Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .process-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }
        .decision {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .action {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        .endpoint {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <h1>User Registration Process</h1>
    
    <div class="process-section">
        <h2>Registration Workflow</h2>
        
        <div class="step action">
            <strong>Step 1:</strong> User visits the registration page
        </div>
        
        <div class="step action">
            <strong>Step 2:</strong> User fills in the registration form with username, email, and password
        </div>
        
        <div class="step decision">
            <strong>Step 3:</strong> System validates the input data
            <ul>
                <li>Check if username is unique</li>
                <li>Validate email format</li>
                <li>Verify password strength</li>
            </ul>
        </div>
        
        <div class="step decision">
            <strong>Decision Point:</strong> Is all data valid?
        </div>
        
        <div class="step action">
            <strong>If Valid:</strong> Create user account in database
        </div>
        
        <div class="step action">
            <strong>Step 4:</strong> Send verification email to user
        </div>
        
        <div class="step action">
            <strong>Step 5:</strong> User clicks verification link in email
        </div>
        
        <div class="step decision">
            <strong>Decision Point:</strong> Is verification link valid and not expired?
        </div>
        
        <div class="step action">
            <strong>If Valid:</strong> Activate user account
        </div>
        
        <div class="step action">
            <strong>Step 6:</strong> Redirect user to login page
        </div>
        
        <div class="step endpoint">
            <strong>End:</strong> Registration process completed successfully
        </div>
        
        <div class="step endpoint">
            <strong>Error Paths:</strong>
            <ul>
                <li>If data validation fails → Show error message → Return to form</li>
                <li>If verification link is invalid → Show error page</li>
                <li>If email sending fails → Show retry option</li>
            </ul>
        </div>
    </div>
    
    <div class="process-section">
        <h2>E-commerce Order Processing</h2>
        
        <div class="step action">
            <strong>Step 1:</strong> Customer adds items to shopping cart
        </div>
        
        <div class="step action">
            <strong>Step 2:</strong> Customer proceeds to checkout
        </div>
        
        <div class="step action">
            <strong>Step 3:</strong> Enter shipping and billing information
        </div>
        
        <div class="step action">
            <strong>Step 4:</strong> Select payment method
        </div>
        
        <div class="step decision">
            <strong>Step 5:</strong> Process payment
        </div>
        
        <div class="step decision">
            <strong>Decision:</strong> Payment successful?
        </div>
        
        <div class="step action">
            <strong>If Success:</strong> Create order record
        </div>
        
        <div class="step action">
            <strong>Step 6:</strong> Send order confirmation email
        </div>
        
        <div class="step action">
            <strong>Step 7:</strong> Update inventory
        </div>
        
        <div class="step action">
            <strong>Step 8:</strong> Generate shipping label
        </div>
        
        <div class="step action">
            <strong>Step 9:</strong> Ship order to customer
        </div>
        
        <div class="step endpoint">
            <strong>End:</strong> Order fulfilled successfully
        </div>
        
        <div class="step endpoint">
            <strong>Error Path:</strong> If payment fails → Show error message → Return to payment page
        </div>
    </div>
    
    <div class="process-section">
        <h2>Software Development Lifecycle</h2>
        
        <div class="step action">
            <strong>Phase 1:</strong> Requirements gathering and analysis
        </div>
        
        <div class="step action">
            <strong>Phase 2:</strong> System design and architecture planning
        </div>
        
        <div class="step action">
            <strong>Phase 3:</strong> Implementation and coding
        </div>
        
        <div class="step action">
            <strong>Phase 4:</strong> Unit testing and integration testing
        </div>
        
        <div class="step decision">
            <strong>Quality Gate:</strong> Do all tests pass?
        </div>
        
        <div class="step action">
            <strong>If Pass:</strong> Deploy to staging environment
        </div>
        
        <div class="step action">
            <strong>Phase 5:</strong> User acceptance testing
        </div>
        
        <div class="step decision">
            <strong>Decision:</strong> UAT approved?
        </div>
        
        <div class="step action">
            <strong>If Approved:</strong> Deploy to production
        </div>
        
        <div class="step action">
            <strong>Phase 6:</strong> Monitor and maintain
        </div>
        
        <div class="step endpoint">
            <strong>End:</strong> Software successfully deployed and operational
        </div>
        
        <div class="step endpoint">
            <strong>Iteration:</strong> If tests fail or UAT rejected → Return to appropriate phase for fixes
        </div>
    </div>
    
    <script>
        // Add some dynamic content for testing
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded for flowchart generation');
            
            // Simulate some user interaction
            setTimeout(() => {
                const newStep = document.createElement('div');
                newStep.className = 'step action';
                newStep.innerHTML = '<strong>Dynamic Step:</strong> This content was added dynamically to test content extraction';
                document.querySelector('.process-section').appendChild(newStep);
            }, 1000);
        });
    </script>
</body>
</html>
