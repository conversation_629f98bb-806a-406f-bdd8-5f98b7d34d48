/**
 * Playwright配置文件 - Gemini Web Summarizer扩展测试
 */

const { defineConfig, devices } = require('@playwright/test');

module.exports = defineConfig({
  // 测试目录
  testDir: '.',
  
  // 全局测试超时时间
  timeout: 60000,
  
  // 期望超时时间
  expect: {
    timeout: 10000
  },
  
  // 失败时重试次数
  retries: process.env.CI ? 2 : 1,
  
  // 并行工作进程数
  workers: 1, // 扩展测试建议使用单进程避免冲突
  
  // 报告器配置
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['list']
  ],
  
  // 全局设置
  use: {
    // 基础URL
    baseURL: 'https://example.com',
    
    // 浏览器设置
    headless: false, // 显示浏览器窗口
    viewport: { width: 1280, height: 720 },
    
    // 忽略HTTPS错误
    ignoreHTTPSErrors: true,
    
    // 截图设置
    screenshot: 'only-on-failure',
    
    // 视频录制
    video: 'retain-on-failure',
    
    // 追踪设置
    trace: 'retain-on-failure',
    
    // 用户代理
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  },

  // 项目配置
  projects: [
    {
      name: 'chromium-extension',
      use: { 
        ...devices['Desktop Chrome'],
        // 扩展测试专用设置
        launchOptions: {
          args: [
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-sandbox',
            '--disable-dev-shm-usage'
          ]
        }
      },
    }
  ],

  // 输出目录
  outputDir: 'test-results/artifacts',
  
  // 全局设置和拆卸
  globalSetup: require.resolve('./global-setup.js'),
  globalTeardown: require.resolve('./global-teardown.js'),
});
