{"name": "typescript", "author": "Microsoft Corp.", "homepage": "https://www.typescriptlang.org/", "version": "5.7.3", "license": "Apache-2.0", "description": "TypeScript is a language for application scale JavaScript development", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript"], "bugs": {"url": "https://github.com/microsoft/TypeScript/issues"}, "repository": {"type": "git", "url": "https://github.com/microsoft/TypeScript.git"}, "main": "./lib/typescript.js", "typings": "./lib/typescript.d.ts", "bin": {"tsc": "./bin/tsc", "tsserver": "./bin/tsserver"}, "engines": {"node": ">=14.17"}, "files": ["bin", "lib", "!lib/enu", "LICENSE.txt", "README.md", "SECURITY.md", "ThirdPartyNoticeText.txt", "!**/.gitattributes"], "devDependencies": {"@dprint/formatter": "^0.4.1", "@dprint/typescript": "0.93.0", "@esfx/canceltoken": "^1.0.0", "@eslint/js": "^9.11.1", "@octokit/rest": "^21.0.2", "@types/chai": "^4.3.20", "@types/diff": "^5.2.2", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.8", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.4", "@typescript-eslint/rule-tester": "^8.8.0", "@typescript-eslint/type-utils": "^8.8.0", "@typescript-eslint/utils": "^8.8.0", "azure-devops-node-api": "^14.1.0", "c8": "^10.1.2", "chai": "^4.5.0", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.47.2", "esbuild": "^0.24.0", "eslint": "^9.11.1", "eslint-formatter-autolinkable-stylish": "^1.4.0", "eslint-plugin-regexp": "^2.6.0", "fast-xml-parser": "^4.5.0", "glob": "^10.4.5", "globals": "^15.9.0", "hereby": "^1.10.0", "jsonc-parser": "^3.3.1", "knip": "^5.30.6", "minimist": "^1.2.8", "mocha": "^10.7.3", "mocha-fivemat-progress-reporter": "^0.1.0", "monocart-coverage-reports": "^2.11.0", "ms": "^2.1.3", "playwright": "^1.47.2", "source-map-support": "^0.5.21", "tslib": "^2.7.0", "typescript": "^5.6.2", "typescript-eslint": "^8.8.0", "which": "^3.0.1"}, "overrides": {"typescript@*": "$typescript"}, "scripts": {"test": "hereby runtests-parallel --light=false", "test:eslint-rules": "hereby run-eslint-rules-tests", "build": "npm run build:compiler && npm run build:tests", "build:compiler": "hereby local", "build:tests": "hereby tests", "build:tests:notypecheck": "hereby tests --no-typecheck", "clean": "hereby clean", "gulp": "hereby", "lint": "hereby lint", "knip": "hereby knip", "format": "dprint fmt", "setup-hooks": "node scripts/link-hooks.mjs"}, "browser": {"fs": false, "os": false, "path": false, "crypto": false, "buffer": false, "source-map-support": false, "inspector": false, "perf_hooks": false}, "packageManager": "npm@8.19.4", "volta": {"node": "20.1.0", "npm": "8.19.4"}, "gitHead": "a5e123d9e0690fcea92878ea8a0a382922009fc9"}