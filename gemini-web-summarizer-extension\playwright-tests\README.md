# 🧪 Playwright测试套件 - Gemini Web Summarizer

## 📋 测试概述

这是一个全面的Playwright自动化测试套件，用于测试Gemini Web Summarizer Chrome扩展的各项功能。

## 🎯 测试覆盖范围

### 1. **扩展加载测试**
- ✅ 验证扩展正确加载
- ✅ 检查扩展启用状态
- ✅ 验证manifest.json配置

### 2. **用户界面测试**
- ✅ 弹出窗口UI元素验证
- ✅ 标签页切换功能
- ✅ 按钮和输入框交互
- ✅ 响应式设计测试

### 3. **设置页面测试**
- ✅ API密钥输入和保存
- ✅ 设置项配置
- ✅ 数据持久化验证

### 4. **功能测试**
- ✅ 内容脚本注入
- ✅ 本地Mermaid渲染器
- ✅ 错误处理机制
- ✅ 权限和安全验证

### 5. **性能测试**
- ✅ 加载时间测试
- ✅ 资源使用情况
- ✅ JS代码覆盖率

## 🚀 快速开始

### 前置要求
- Node.js >= 16.0.0
- npm 或 yarn
- Chrome/Chromium 浏览器

### 安装和设置
```bash
# 进入测试目录
cd playwright-tests

# 首次设置（安装依赖和浏览器）
node run-tests.js --setup
```

### 运行测试
```bash
# 运行所有测试（无头模式）
node run-tests.js

# 显示浏览器窗口运行测试
node run-tests.js --headed

# 调试模式
node run-tests.js --debug

# 交互式UI模式
node run-tests.js --ui

# 运行特定测试
node run-tests.js --grep="弹出窗口"

# 查看测试报告
node run-tests.js --report
```

## 📁 文件结构

```
playwright-tests/
├── extension-test.js          # 主测试文件
├── playwright.config.js      # Playwright配置
├── global-setup.js           # 全局设置
├── global-teardown.js        # 全局清理
├── run-tests.js              # 测试运行脚本
├── package.json              # 测试依赖
├── README.md                 # 测试文档
└── test-results/             # 测试结果目录
    ├── html-report/          # HTML报告
    ├── screenshots/          # 失败截图
    ├── videos/              # 测试视频
    ├── traces/              # 执行追踪
    └── results.json         # JSON结果
```

## 🧪 测试详情

### 测试1: 扩展加载测试
```javascript
test('1. 扩展加载测试', async () => {
    // 验证扩展ID存在
    expect(extensionId).toBeTruthy();
    
    // 检查扩展启用状态
    const isEnabled = await toggleButton.isChecked();
    expect(isEnabled).toBe(true);
});
```

### 测试2: 弹出窗口UI测试
```javascript
test('2. 弹出窗口UI测试', async () => {
    // 验证UI元素
    await expect(page.locator('h1')).toContainText('Gemini Web Summarizer');
    await expect(page.locator('#summarize-btn')).toBeVisible();
    
    // 测试标签页切换
    await page.locator('#summary-tab').click();
    await expect(page.locator('#summary-content')).toBeVisible();
});
```

### 测试3: 设置页面测试
```javascript
test('3. 设置页面测试', async () => {
    // 测试API密钥输入
    await page.locator('#api-key').fill(TEST_CONFIG.mockApiKey);
    await expect(page.locator('#api-key')).toHaveValue(TEST_CONFIG.mockApiKey);
    
    // 测试显示/隐藏功能
    await page.locator('#toggle-api-key').click();
    const inputType = await page.locator('#api-key').getAttribute('type');
    expect(inputType).toBe('text');
});
```

## 📊 测试报告

### HTML报告
运行测试后，会生成详细的HTML报告：
- 测试结果概览
- 失败测试详情
- 截图和视频
- 执行时间统计

### JSON报告
结构化的测试结果数据：
```json
{
  "stats": {
    "total": 10,
    "passed": 8,
    "failed": 2,
    "skipped": 0,
    "duration": 45000
  }
}
```

## 🔧 配置选项

### Playwright配置
```javascript
module.exports = defineConfig({
  timeout: 60000,           // 全局超时
  retries: 1,              // 重试次数
  workers: 1,              // 并行进程数
  use: {
    headless: false,       // 显示浏览器
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure'
  }
});
```

### 测试配置
```javascript
const TEST_CONFIG = {
    testPageUrl: 'https://example.com',
    mockApiKey: 'AIza' + 'x'.repeat(35),
    timeout: 30000
};
```

## 🐛 调试指南

### 调试失败的测试
```bash
# 调试模式运行特定测试
node run-tests.js --debug --grep="失败的测试名称"

# 查看失败截图
# 位置: test-results/screenshots/

# 查看失败视频
# 位置: test-results/videos/

# 查看执行追踪
# 位置: test-results/traces/
```

### 常见问题

#### 1. 扩展加载失败
```bash
# 检查manifest.json语法
node -e "JSON.parse(require('fs').readFileSync('../manifest.json', 'utf8')); console.log('JSON语法正确');"

# 检查扩展文件完整性
ls -la ../
```

#### 2. 测试超时
```javascript
// 增加特定测试的超时时间
test('长时间运行的测试', async () => {
    test.setTimeout(120000); // 2分钟
    // 测试代码...
});
```

#### 3. 元素定位失败
```javascript
// 使用更稳定的定位器
await page.waitForSelector('#element-id', { timeout: 10000 });
await expect(page.locator('#element-id')).toBeVisible();
```

## 📈 持续集成

### GitHub Actions示例
```yaml
name: Extension Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: |
          cd playwright-tests
          npm install
          npx playwright install chromium
      - name: Run tests
        run: |
          cd playwright-tests
          npm test
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results
          path: playwright-tests/test-results/
```

## 🎯 最佳实践

### 1. 测试编写
- 使用描述性的测试名称
- 每个测试保持独立性
- 适当使用等待和超时
- 验证关键功能点

### 2. 页面对象模式
```javascript
class PopupPage {
    constructor(page) {
        this.page = page;
        this.summarizeBtn = page.locator('#summarize-btn');
        this.statusMessage = page.locator('#status-message');
    }
    
    async clickSummarize() {
        await this.summarizeBtn.click();
    }
    
    async getStatus() {
        return await this.statusMessage.textContent();
    }
}
```

### 3. 数据驱动测试
```javascript
const testCases = [
    { input: 'test1', expected: 'result1' },
    { input: 'test2', expected: 'result2' }
];

for (const testCase of testCases) {
    test(`测试用例: ${testCase.input}`, async () => {
        // 测试逻辑
    });
}
```

## 📞 支持和反馈

如果在运行测试时遇到问题：

1. 查看测试报告中的错误详情
2. 检查截图和视频记录
3. 查看控制台输出
4. 参考调试指南

---

**测试套件版本**: 1.0.0  
**最后更新**: 2025年6月30日  
**兼容性**: Chrome/Edge扩展 Manifest V3
