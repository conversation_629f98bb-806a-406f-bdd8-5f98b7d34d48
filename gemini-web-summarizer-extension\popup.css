/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 600px;
}

.container {
    width: 420px;
    min-height: 600px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

/* 头部样式 */
header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    padding: 20px;
    text-align: center;
    color: white;
}

header h1 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

/* 标签页样式 */
.tab-container {
    padding: 0;
}

.tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab-button {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
}

.tab-button:hover {
    background: #e9ecef;
    color: #495057;
}

.tab-button.active {
    background: white;
    color: #007bff;
    border-bottom: 2px solid #007bff;
}

.tab-content {
    padding: 20px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 按钮样式 */
.action-buttons {
    margin-bottom: 20px;
}

.primary-btn {
    width: 100%;
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.primary-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 结果容器样式 */
.result-container {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #e9ecef;
    border-bottom: 1px solid #dee2e6;
}

.result-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.copy-btn, .control-btn {
    padding: 6px 10px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
    margin-left: 4px;
}

.copy-btn:hover, .control-btn:hover {
    background: #0056b3;
}

.chart-controls {
    display: flex;
    gap: 4px;
}

.content-area {
    padding: 16px;
    max-height: 300px;
    overflow-y: auto;
    line-height: 1.6;
    font-size: 14px;
    color: #495057;
    background: white;
}

/* 图表容器样式 */
.chart-container {
    background: white;
    padding: 16px;
    max-height: 400px;
    overflow: auto;
    position: relative;
}

#mermaid-chart {
    text-align: center;
    min-height: 200px;
    cursor: grab;
}

#mermaid-chart:active {
    cursor: grabbing;
}

/* 快速设置栏样式 */
.quick-settings {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
    gap: 15px;
}

.quick-setting-item {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.quick-setting-item label {
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    white-space: nowrap;
    min-width: 35px;
}

.quick-select {
    flex: 1;
    padding: 6px 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 12px;
    background: white;
    color: #495057;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.quick-select:hover {
    border-color: #adb5bd;
}

/* 状态栏样式 */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.status-message {
    font-size: 12px;
    color: #6c757d;
    flex: 1;
}

.settings-btn {
    padding: 8px 12px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.settings-btn:hover {
    background: #5a6268;
}

/* API密钥提示样式 */
.api-prompt {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.prompt-content {
    text-align: center;
    padding: 30px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    max-width: 300px;
}

.prompt-content h3 {
    margin-bottom: 12px;
    color: #495057;
    font-size: 16px;
}

.prompt-content p {
    margin-bottom: 20px;
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
