{"name": "gemini-web-summarizer-tests", "version": "1.0.0", "description": "Playwright测试套件 - Gemini Web Summarizer扩展", "main": "extension-test.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:report": "playwright show-report", "install-browsers": "playwright install chromium", "test:single": "playwright test --grep", "test:parallel": "playwright test --workers=1", "test:retry": "playwright test --retries=2"}, "keywords": ["playwright", "testing", "chrome-extension", "gemini", "automation"], "author": "Gemini Web Summarizer Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-repo/gemini-web-summarizer"}, "bugs": {"url": "https://github.com/your-repo/gemini-web-summarizer/issues"}, "homepage": "https://github.com/your-repo/gemini-web-summarizer#readme"}