<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流程图功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        input[type="password"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            font-family: inherit;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        #mermaid-display {
            border: 1px solid #ddd;
            padding: 20px;
            background: white;
            border-radius: 8px;
            text-align: center;
            min-height: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 流程图功能测试工具</h1>
        
        <div class="test-section">
            <h2>1. API连接测试</h2>
            <input type="password" id="api-key" placeholder="请输入Gemini API密钥">
            <button id="test-api" class="btn">测试API连接</button>
            <div id="api-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>2. 流程图生成测试</h2>
            <textarea id="test-content" placeholder="请输入要生成流程图的内容...">
用户注册流程：
1. 用户访问注册页面
2. 填写用户名、邮箱和密码
3. 系统验证信息格式
4. 如果格式正确，发送验证邮件
5. 用户点击邮件中的验证链接
6. 系统激活账户
7. 注册完成，跳转到登录页面
            </textarea>
            <button id="generate-flowchart" class="btn">生成流程图</button>
            <div id="flowchart-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>3. Mermaid渲染测试</h2>
            <textarea id="mermaid-code" placeholder="请输入Mermaid代码...">
flowchart TD
    A[开始] --> B[用户输入]
    B --> C{验证输入}
    C -->|有效| D[处理请求]
    C -->|无效| E[显示错误]
    D --> F[返回结果]
    E --> B
    F --> G[结束]
            </textarea>
            <button id="render-mermaid" class="btn">渲染图表</button>
            <div id="mermaid-display"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose'
        });

        const elements = {
            apiKey: document.getElementById('api-key'),
            testApi: document.getElementById('test-api'),
            apiResult: document.getElementById('api-result'),
            testContent: document.getElementById('test-content'),
            generateFlowchart: document.getElementById('generate-flowchart'),
            flowchartResult: document.getElementById('flowchart-result'),
            mermaidCode: document.getElementById('mermaid-code'),
            renderMermaid: document.getElementById('render-mermaid'),
            mermaidDisplay: document.getElementById('mermaid-display')
        };

        // 显示结果
        function showResult(element, message, type = 'info') {
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        // API连接测试
        elements.testApi.addEventListener('click', async () => {
            const apiKey = elements.apiKey.value.trim();
            if (!apiKey) {
                showResult(elements.apiResult, '请输入API密钥', 'error');
                return;
            }

            elements.testApi.disabled = true;
            showResult(elements.apiResult, '正在测试API连接...', 'info');

            try {
                const response = await testGeminiAPI(apiKey);
                showResult(elements.apiResult, `✅ API连接成功！\n响应: ${response}`, 'success');
            } catch (error) {
                showResult(elements.apiResult, `❌ API连接失败: ${error.message}`, 'error');
            } finally {
                elements.testApi.disabled = false;
            }
        });

        // 流程图生成测试
        elements.generateFlowchart.addEventListener('click', async () => {
            const apiKey = elements.apiKey.value.trim();
            const content = elements.testContent.value.trim();
            
            if (!apiKey) {
                showResult(elements.flowchartResult, '请先输入API密钥', 'error');
                return;
            }
            
            if (!content) {
                showResult(elements.flowchartResult, '请输入要生成流程图的内容', 'error');
                return;
            }

            elements.generateFlowchart.disabled = true;
            showResult(elements.flowchartResult, '正在生成流程图...', 'info');

            try {
                const mermaidCode = await generateFlowchartCode(apiKey, content);
                showResult(elements.flowchartResult, `✅ 流程图生成成功！\n\nMermaid代码:\n${mermaidCode}`, 'success');
                
                // 自动填充到渲染测试区域
                elements.mermaidCode.value = mermaidCode;
            } catch (error) {
                showResult(elements.flowchartResult, `❌ 流程图生成失败: ${error.message}`, 'error');
            } finally {
                elements.generateFlowchart.disabled = false;
            }
        });

        // Mermaid渲染测试
        elements.renderMermaid.addEventListener('click', async () => {
            const mermaidCode = elements.mermaidCode.value.trim();
            
            if (!mermaidCode) {
                elements.mermaidDisplay.innerHTML = '<p style="color: #dc3545;">请输入Mermaid代码</p>';
                return;
            }

            elements.renderMermaid.disabled = true;
            elements.mermaidDisplay.innerHTML = '<p>正在渲染图表...</p>';

            try {
                const chartId = `chart-${Date.now()}`;
                const { svg } = await mermaid.render(chartId, mermaidCode);
                elements.mermaidDisplay.innerHTML = svg;
            } catch (error) {
                elements.mermaidDisplay.innerHTML = `<p style="color: #dc3545;">渲染失败: ${error.message}</p>`;
            } finally {
                elements.renderMermaid.disabled = false;
            }
        });

        // 测试Gemini API
        async function testGeminiAPI(apiKey) {
            const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=${apiKey}`;
            
            const requestBody = {
                contents: [{
                    parts: [{
                        text: 'Hello, please respond with "API connection successful"'
                    }]
                }],
                generationConfig: {
                    maxOutputTokens: 50,
                    temperature: 0.1,
                    thinkingConfig: { thinkingBudget: 0 }
                }
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`${response.status} ${response.statusText}: ${JSON.stringify(errorData)}`);
            }

            const data = await response.json();
            return data.candidates[0].content.parts[0].text;
        }

        // 生成流程图代码
        async function generateFlowchartCode(apiKey, content) {
            const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=${apiKey}`;
            
            const prompt = `基于以下内容，生成一个Mermaid格式的流程图。

要求：
1. 使用 flowchart TD 语法开头
2. 节点名称使用中文，保持简洁
3. 使用 --> 连接节点
4. 只返回纯Mermaid代码，不要任何解释文字
5. 不要使用代码块标记（\`\`\`）

示例格式：
flowchart TD
    A[开始] --> B[步骤1]
    B --> C{判断条件}
    C -->|是| D[步骤2]
    C -->|否| E[步骤3]
    D --> F[结束]
    E --> F

内容：${content}

请直接返回Mermaid代码：`;

            const requestBody = {
                contents: [{
                    parts: [{ text: prompt }]
                }],
                generationConfig: {
                    maxOutputTokens: 1000,
                    temperature: 0.7,
                    thinkingConfig: { thinkingBudget: 0 }
                }
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`${response.status} ${response.statusText}: ${JSON.stringify(errorData)}`);
            }

            const data = await response.json();
            const responseText = data.candidates[0].content.parts[0].text;
            
            // 提取Mermaid代码
            const mermaidMatch = responseText.match(/```mermaid\n([\s\S]*?)\n```/) || 
                                responseText.match(/```\n([\s\S]*?)\n```/) ||
                                [null, responseText];
            
            return (mermaidMatch[1] || responseText).trim();
        }
    </script>
</body>
</html>
