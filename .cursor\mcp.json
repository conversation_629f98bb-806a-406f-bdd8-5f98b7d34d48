{"mcpServers": {"filesystem": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-filesystem", "D:\\CodeProjects\\cursor"], "enabled": true}, "mcp-server-firecrawl": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_URL": "http://localhost:3002"}, "enabled": true}, "browser-tools": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "@agentdeskai/browser-tools-mcp@1.2.0"], "enabled": true}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "enabled": true}, "ai-image-gen": {"command": "node", "args": ["D:\\CodeProjects\\cursor\\ai-image-gen-mcp-main\\dist\\server.js"]}}}