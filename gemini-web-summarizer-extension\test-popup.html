<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹出窗口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 弹出窗口功能测试</h1>
        
        <h2>测试步骤</h2>
        <ol>
            <li>重新加载扩展</li>
            <li>点击扩展图标打开弹出窗口</li>
            <li>按F12打开开发者工具</li>
            <li>在控制台中运行测试命令</li>
        </ol>
        
        <h2>测试命令</h2>
        <div>
            <button class="btn" onclick="copyCommand('testFixed()')">复制: testFixed()</button>
            <button class="btn" onclick="copyCommand('debugFixed()')">复制: debugFixed()</button>
            <button class="btn" onclick="copyCommand('document.getElementById(\'settings-btn\').click()')">复制: 手动点击设置</button>
        </div>
        
        <h2>预期结果</h2>
        <ul>
            <li>✅ 控制台显示"修复版popup.js加载完成"</li>
            <li>✅ 控制台显示"DOM元素初始化完成"</li>
            <li>✅ 控制台显示"扩展初始化完成"</li>
            <li>✅ 点击流程图标签页能正常切换</li>
            <li>✅ 点击设置按钮能打开设置页面</li>
            <li>❌ 如果仍有错误，查看控制台错误信息</li>
        </ul>
        
        <h2>故障排除</h2>
        <div>
            <h3>如果设置按钮仍无反应：</h3>
            <pre class="log">// 在控制台运行以下命令
// 1. 检查元素是否存在
console.log(document.getElementById('settings-btn'));

// 2. 手动绑定事件
document.getElementById('settings-btn').addEventListener('click', function() {
    console.log('手动绑定的设置按钮被点击');
    chrome.runtime.openOptionsPage();
});

// 3. 直接打开设置页面
chrome.runtime.openOptionsPage();</pre>
            
            <h3>如果标签页切换无反应：</h3>
            <pre class="log">// 在控制台运行以下命令
// 1. 检查标签页按钮
console.log(document.querySelectorAll('.tab-button'));

// 2. 手动切换标签页
document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
document.querySelector('[data-tab="flowchart"]').classList.add('active');
document.getElementById('flowchart-tab').classList.add('active');</pre>
        </div>
        
        <h2>完整重置</h2>
        <div>
            <p>如果所有方法都失败，请：</p>
            <ol>
                <li>删除扩展</li>
                <li>重新加载扩展文件夹</li>
                <li>确保所有文件都已保存</li>
                <li>检查manifest.json是否正确</li>
            </ol>
        </div>
    </div>

    <script>
        function copyCommand(command) {
            navigator.clipboard.writeText(command).then(() => {
                alert('命令已复制到剪贴板: ' + command);
            }).catch(err => {
                console.error('复制失败:', err);
                // 备用方案
                const textArea = document.createElement('textarea');
                textArea.value = command;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('命令已复制到剪贴板: ' + command);
            });
        }
    </script>
</body>
</html>
