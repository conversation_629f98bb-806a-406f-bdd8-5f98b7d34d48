/**
 * 设置页面脚本
 * 处理扩展设置的保存、加载和验证
 */

// DOM元素引用
const elements = {
    // API设置
    apiKey: document.getElementById('api-key'),
    toggleApiKey: document.getElementById('toggle-api-key'),
    validateApiKey: document.getElementById('validate-api-key'),
    apiValidationResult: document.getElementById('api-validation-result'),
    
    // 功能设置
    extensionEnabled: document.getElementById('extension-enabled'),
    autoSummarize: document.getElementById('auto-summarize'),
    summaryLength: document.getElementById('summary-length'),
    language: document.getElementById('language'),
    
    // 高级设置
    apiTemperature: document.getElementById('api-temperature'),
    temperatureValue: document.getElementById('temperature-value'),
    maxTokens: document.getElementById('max-tokens'),
    
    // 数据管理
    summaryCount: document.getElementById('summary-count'),
    flowchartCount: document.getElementById('flowchart-count'),
    usageDays: document.getElementById('usage-days'),
    exportSettings: document.getElementById('export-settings'),
    importSettings: document.getElementById('import-settings'),
    importFile: document.getElementById('import-file'),
    resetSettings: document.getElementById('reset-settings'),
    
    // 保存和状态
    saveSettings: document.getElementById('save-settings'),
    saveStatus: document.getElementById('save-status'),
    
    // 确认对话框
    confirmDialog: document.getElementById('confirm-dialog'),
    confirmTitle: document.getElementById('confirm-title'),
    confirmMessage: document.getElementById('confirm-message'),
    confirmYes: document.getElementById('confirm-yes'),
    confirmNo: document.getElementById('confirm-no')
};

// 默认设置
const defaultSettings = {
    geminiApiKey: '',
    extensionEnabled: true,
    autoSummarize: false,
    summaryLength: 'medium',
    language: 'zh-CN',
    apiTemperature: 0.7,
    maxTokens: 2048
};

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    await loadSettings();
    setupEventListeners();
    await loadUsageStats();
});

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // API密钥显示/隐藏切换
    elements.toggleApiKey.addEventListener('click', toggleApiKeyVisibility);
    
    // API密钥验证
    elements.validateApiKey.addEventListener('click', handleValidateApiKey);
    
    // 温度滑块
    elements.apiTemperature.addEventListener('input', updateTemperatureValue);
    
    // 数据管理按钮
    elements.exportSettings.addEventListener('click', handleExportSettings);
    elements.importSettings.addEventListener('click', () => elements.importFile.click());
    elements.importFile.addEventListener('change', handleImportSettings);
    elements.resetSettings.addEventListener('click', handleResetSettings);
    
    // 保存设置
    elements.saveSettings.addEventListener('click', handleSaveSettings);
    
    // 确认对话框
    elements.confirmYes.addEventListener('click', handleConfirmYes);
    elements.confirmNo.addEventListener('click', hideConfirmDialog);
    
    // 实时保存某些设置
    elements.extensionEnabled.addEventListener('change', autoSave);
    elements.autoSummarize.addEventListener('change', autoSave);
}

/**
 * 加载设置
 */
async function loadSettings() {
    try {
        const result = await chrome.storage.sync.get(Object.keys(defaultSettings));
        const settings = { ...defaultSettings, ...result };
        
        // 填充表单
        elements.apiKey.value = settings.geminiApiKey || '';
        elements.extensionEnabled.checked = settings.extensionEnabled;
        elements.autoSummarize.checked = settings.autoSummarize;
        elements.summaryLength.value = settings.summaryLength;
        elements.language.value = settings.language;
        elements.apiTemperature.value = settings.apiTemperature;
        elements.maxTokens.value = settings.maxTokens;
        
        // 更新温度显示
        updateTemperatureValue();
        
        showStatus('设置已加载', 'success');
        
    } catch (error) {
        console.error('加载设置失败:', error);
        showStatus('加载设置失败', 'error');
    }
}

/**
 * 保存设置
 */
async function handleSaveSettings() {
    setButtonLoading(elements.saveSettings, true);
    
    try {
        const settings = {
            geminiApiKey: elements.apiKey.value.trim(),
            extensionEnabled: elements.extensionEnabled.checked,
            autoSummarize: elements.autoSummarize.checked,
            summaryLength: elements.summaryLength.value,
            language: elements.language.value,
            apiTemperature: parseFloat(elements.apiTemperature.value),
            maxTokens: parseInt(elements.maxTokens.value)
        };
        
        // 验证设置
        if (!validateSettings(settings)) {
            return;
        }
        
        // 保存到Chrome存储
        await chrome.storage.sync.set(settings);
        
        showStatus('设置已保存', 'success');
        
    } catch (error) {
        console.error('保存设置失败:', error);
        showStatus('保存设置失败', 'error');
    } finally {
        setButtonLoading(elements.saveSettings, false);
    }
}

/**
 * 自动保存重要设置
 */
async function autoSave() {
    try {
        const settings = {
            extensionEnabled: elements.extensionEnabled.checked,
            autoSummarize: elements.autoSummarize.checked
        };
        
        await chrome.storage.sync.set(settings);
        
    } catch (error) {
        console.error('自动保存失败:', error);
    }
}

/**
 * 验证设置
 */
function validateSettings(settings) {
    if (settings.extensionEnabled && !settings.geminiApiKey) {
        showStatus('启用扩展功能需要设置API密钥', 'error');
        return false;
    }
    
    if (settings.apiTemperature < 0 || settings.apiTemperature > 1) {
        showStatus('温度值必须在0-1之间', 'error');
        return false;
    }
    
    if (settings.maxTokens < 100 || settings.maxTokens > 8192) {
        showStatus('最大输出长度必须在100-8192之间', 'error');
        return false;
    }
    
    return true;
}

/**
 * 切换API密钥显示/隐藏
 */
function toggleApiKeyVisibility() {
    const isPassword = elements.apiKey.type === 'password';
    elements.apiKey.type = isPassword ? 'text' : 'password';
    elements.toggleApiKey.textContent = isPassword ? '🙈' : '👁️';
}

/**
 * 验证API密钥
 */
async function handleValidateApiKey() {
    const apiKey = elements.apiKey.value.trim();
    
    if (!apiKey) {
        showValidationResult('请先输入API密钥', 'error');
        return;
    }
    
    setButtonLoading(elements.validateApiKey, true);
    
    try {
        const isValid = await validateGeminiApiKey(apiKey);
        
        if (isValid) {
            showValidationResult('✅ API密钥验证成功', 'success');
        } else {
            showValidationResult('❌ API密钥验证失败，请检查密钥是否正确', 'error');
        }
        
    } catch (error) {
        console.error('API密钥验证失败:', error);
        showValidationResult('❌ 验证过程中发生错误，请检查网络连接', 'error');
    } finally {
        setButtonLoading(elements.validateApiKey, false);
    }
}

/**
 * 验证Gemini API密钥
 */
async function validateGeminiApiKey(apiKey) {
    const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`;
    
    const requestBody = {
        contents: [{
            parts: [{
                text: '测试连接'
            }]
        }],
        generationConfig: {
            maxOutputTokens: 10
        }
    };
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });
        
        return response.ok;
        
    } catch (error) {
        console.error('API验证请求失败:', error);
        return false;
    }
}

/**
 * 更新温度值显示
 */
function updateTemperatureValue() {
    elements.temperatureValue.textContent = elements.apiTemperature.value;
}

/**
 * 加载使用统计
 */
async function loadUsageStats() {
    try {
        const result = await chrome.storage.local.get(['usageStats']);
        const stats = result.usageStats || {};
        
        let summaryCount = 0;
        let flowchartCount = 0;
        let usageDays = 0;
        
        // 计算统计数据
        for (const date in stats) {
            usageDays++;
            const dayStats = stats[date];
            summaryCount += dayStats.summarize || 0;
            flowchartCount += dayStats.generateFlowchart || 0;
        }
        
        // 更新显示
        elements.summaryCount.textContent = summaryCount;
        elements.flowchartCount.textContent = flowchartCount;
        elements.usageDays.textContent = usageDays;
        
    } catch (error) {
        console.error('加载使用统计失败:', error);
    }
}

/**
 * 导出设置
 */
async function handleExportSettings() {
    try {
        const settings = await chrome.storage.sync.get();
        const exportData = {
            version: '1.0.0',
            timestamp: new Date().toISOString(),
            settings: settings
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `gemini-summarizer-settings-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showStatus('设置已导出', 'success');
        
    } catch (error) {
        console.error('导出设置失败:', error);
        showStatus('导出设置失败', 'error');
    }
}

/**
 * 导入设置
 */
async function handleImportSettings(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
        const text = await file.text();
        const importData = JSON.parse(text);
        
        if (!importData.settings) {
            throw new Error('无效的设置文件格式');
        }
        
        // 验证导入的设置
        const settings = { ...defaultSettings, ...importData.settings };
        
        if (!validateSettings(settings)) {
            return;
        }
        
        // 保存设置
        await chrome.storage.sync.set(settings);
        
        // 重新加载页面设置
        await loadSettings();
        
        showStatus('设置已导入', 'success');
        
    } catch (error) {
        console.error('导入设置失败:', error);
        showStatus('导入设置失败：' + error.message, 'error');
    } finally {
        // 清空文件输入
        elements.importFile.value = '';
    }
}

/**
 * 重置设置
 */
function handleResetSettings() {
    showConfirmDialog(
        '重置设置',
        '确定要重置所有设置到默认值吗？此操作不可撤销。',
        async () => {
            try {
                await chrome.storage.sync.clear();
                await chrome.storage.local.clear();
                await loadSettings();
                await loadUsageStats();
                showStatus('设置已重置', 'success');
            } catch (error) {
                console.error('重置设置失败:', error);
                showStatus('重置设置失败', 'error');
            }
        }
    );
}

// 全局确认回调
let confirmCallback = null;

/**
 * 显示确认对话框
 */
function showConfirmDialog(title, message, callback) {
    elements.confirmTitle.textContent = title;
    elements.confirmMessage.textContent = message;
    elements.confirmDialog.style.display = 'flex';
    confirmCallback = callback;
}

/**
 * 隐藏确认对话框
 */
function hideConfirmDialog() {
    elements.confirmDialog.style.display = 'none';
    confirmCallback = null;
}

/**
 * 处理确认
 */
function handleConfirmYes() {
    hideConfirmDialog();
    if (confirmCallback) {
        confirmCallback();
    }
}

/**
 * 显示验证结果
 */
function showValidationResult(message, type) {
    elements.apiValidationResult.textContent = message;
    elements.apiValidationResult.className = `validation-result ${type}`;
    elements.apiValidationResult.style.display = 'block';
    
    // 3秒后自动隐藏
    setTimeout(() => {
        elements.apiValidationResult.style.display = 'none';
    }, 3000);
}

/**
 * 显示状态消息
 */
function showStatus(message, type) {
    elements.saveStatus.textContent = message;
    elements.saveStatus.className = `save-status ${type}`;
    
    // 3秒后清除状态
    setTimeout(() => {
        elements.saveStatus.textContent = '';
        elements.saveStatus.className = 'save-status';
    }, 3000);
}

/**
 * 设置按钮加载状态
 */
function setButtonLoading(button, isLoading) {
    const textSpan = button.querySelector('.btn-text');
    const spinner = button.querySelector('.loading-spinner');
    
    if (isLoading) {
        textSpan.style.display = 'none';
        spinner.style.display = 'inline';
        button.disabled = true;
    } else {
        textSpan.style.display = 'inline';
        spinner.style.display = 'none';
        button.disabled = false;
    }
}
