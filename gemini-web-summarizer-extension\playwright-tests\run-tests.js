#!/usr/bin/env node

/**
 * 测试运行脚本 - 自动化运行Playwright测试
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorLog(color, message) {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

async function checkPrerequisites() {
    colorLog('cyan', '🔍 检查测试前置条件...');
    
    // 检查Node.js版本
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 16) {
        colorLog('red', `❌ Node.js版本过低: ${nodeVersion}，需要 >= 16.0.0`);
        return false;
    }
    colorLog('green', `✅ Node.js版本: ${nodeVersion}`);
    
    // 检查扩展文件
    const extensionPath = path.resolve(__dirname, '..');
    const manifestPath = path.join(extensionPath, 'manifest.json');
    
    if (!fs.existsSync(manifestPath)) {
        colorLog('red', '❌ 找不到manifest.json文件');
        return false;
    }
    colorLog('green', '✅ 扩展文件存在');
    
    // 检查package.json
    const packagePath = path.join(__dirname, 'package.json');
    if (!fs.existsSync(packagePath)) {
        colorLog('red', '❌ 找不到测试package.json文件');
        return false;
    }
    colorLog('green', '✅ 测试配置文件存在');
    
    return true;
}

async function installDependencies() {
    colorLog('cyan', '📦 安装测试依赖...');
    
    return new Promise((resolve, reject) => {
        const npm = spawn('npm', ['install'], {
            cwd: __dirname,
            stdio: 'inherit'
        });
        
        npm.on('close', (code) => {
            if (code === 0) {
                colorLog('green', '✅ 依赖安装完成');
                resolve();
            } else {
                colorLog('red', '❌ 依赖安装失败');
                reject(new Error(`npm install failed with code ${code}`));
            }
        });
    });
}

async function installBrowsers() {
    colorLog('cyan', '🌐 安装Playwright浏览器...');
    
    return new Promise((resolve, reject) => {
        const playwright = spawn('npx', ['playwright', 'install', 'chromium'], {
            cwd: __dirname,
            stdio: 'inherit'
        });
        
        playwright.on('close', (code) => {
            if (code === 0) {
                colorLog('green', '✅ 浏览器安装完成');
                resolve();
            } else {
                colorLog('red', '❌ 浏览器安装失败');
                reject(new Error(`Browser install failed with code ${code}`));
            }
        });
    });
}

async function runTests(options = {}) {
    colorLog('cyan', '🧪 开始运行测试...');
    
    const args = ['playwright', 'test'];
    
    // 添加选项
    if (options.headed) args.push('--headed');
    if (options.debug) args.push('--debug');
    if (options.ui) args.push('--ui');
    if (options.grep) args.push('--grep', options.grep);
    if (options.workers) args.push('--workers', options.workers);
    if (options.retries) args.push('--retries', options.retries);
    
    return new Promise((resolve, reject) => {
        const test = spawn('npx', args, {
            cwd: __dirname,
            stdio: 'inherit'
        });
        
        test.on('close', (code) => {
            if (code === 0) {
                colorLog('green', '✅ 测试运行完成');
                resolve();
            } else {
                colorLog('yellow', `⚠️ 测试完成，退出码: ${code}`);
                resolve(); // 不reject，因为测试失败是正常情况
            }
        });
    });
}

async function showReport() {
    colorLog('cyan', '📊 打开测试报告...');
    
    return new Promise((resolve) => {
        const report = spawn('npx', ['playwright', 'show-report'], {
            cwd: __dirname,
            stdio: 'inherit'
        });
        
        report.on('close', () => {
            resolve();
        });
    });
}

async function main() {
    try {
        colorLog('bright', '🚀 Gemini Web Summarizer 扩展测试启动器');
        colorLog('bright', '='.repeat(50));
        
        // 解析命令行参数
        const args = process.argv.slice(2);
        const options = {
            headed: args.includes('--headed'),
            debug: args.includes('--debug'),
            ui: args.includes('--ui'),
            report: args.includes('--report'),
            setup: args.includes('--setup'),
            grep: args.find(arg => arg.startsWith('--grep='))?.split('=')[1]
        };
        
        // 检查前置条件
        const prereqsOk = await checkPrerequisites();
        if (!prereqsOk) {
            process.exit(1);
        }
        
        // 如果是首次设置
        if (options.setup || !fs.existsSync(path.join(__dirname, 'node_modules'))) {
            await installDependencies();
            await installBrowsers();
        }
        
        // 只显示报告
        if (options.report) {
            await showReport();
            return;
        }
        
        // 运行测试
        await runTests(options);
        
        // 显示结果摘要
        colorLog('bright', '\n📋 测试完成摘要:');
        colorLog('cyan', '- 测试结果已保存到 test-results/ 目录');
        colorLog('cyan', '- 运行 "node run-tests.js --report" 查看详细报告');
        colorLog('cyan', '- 失败的测试截图和视频已保存');
        
    } catch (error) {
        colorLog('red', `❌ 测试运行失败: ${error.message}`);
        process.exit(1);
    }
}

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🧪 Gemini Web Summarizer 扩展测试工具

用法:
  node run-tests.js [选项]

选项:
  --setup          首次设置，安装依赖和浏览器
  --headed         显示浏览器窗口
  --debug          调试模式
  --ui             交互式UI模式
  --report         只显示测试报告
  --grep=<pattern> 运行匹配的测试
  --help, -h       显示帮助信息

示例:
  node run-tests.js --setup                    # 首次设置
  node run-tests.js --headed                   # 显示浏览器运行测试
  node run-tests.js --grep="弹出窗口"          # 只运行弹出窗口相关测试
  node run-tests.js --debug                    # 调试模式
  node run-tests.js --report                   # 查看测试报告
`);
    process.exit(0);
}

// 运行主程序
main();
