<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini API 调试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #555;
        }
        input[type="password"], input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #007bff;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            color: #856404;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .toggle-btn {
            background: #6c757d;
            font-size: 12px;
            padding: 6px 12px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Gemini API 调试工具</h1>
        
        <div class="form-group">
            <label for="api-key">Gemini API 密钥</label>
            <div style="display: flex; align-items: center;">
                <input type="password" id="api-key" placeholder="请输入您的Gemini API密钥">
                <button type="button" id="toggle-key" class="btn toggle-btn">显示</button>
            </div>
        </div>
        
        <div class="form-group">
            <button id="test-basic" class="btn">基础连接测试</button>
            <button id="test-models" class="btn">获取模型列表</button>
            <button id="test-generate" class="btn">生成内容测试</button>
            <button id="clear-result" class="btn" style="background: #dc3545;">清除结果</button>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const elements = {
            apiKey: document.getElementById('api-key'),
            toggleKey: document.getElementById('toggle-key'),
            testBasic: document.getElementById('test-basic'),
            testModels: document.getElementById('test-models'),
            testGenerate: document.getElementById('test-generate'),
            clearResult: document.getElementById('clear-result'),
            result: document.getElementById('result')
        };

        // 切换API密钥显示
        elements.toggleKey.addEventListener('click', () => {
            const isPassword = elements.apiKey.type === 'password';
            elements.apiKey.type = isPassword ? 'text' : 'password';
            elements.toggleKey.textContent = isPassword ? '隐藏' : '显示';
        });

        // 清除结果
        elements.clearResult.addEventListener('click', () => {
            elements.result.style.display = 'none';
            elements.result.textContent = '';
        });

        // 显示结果
        function showResult(message, type = 'info') {
            elements.result.className = `result ${type}`;
            elements.result.textContent = message;
            elements.result.style.display = 'block';
        }

        // 基础连接测试
        elements.testBasic.addEventListener('click', async () => {
            const apiKey = elements.apiKey.value.trim();
            if (!apiKey) {
                showResult('请先输入API密钥', 'error');
                return;
            }

            showResult('正在测试基础连接...', 'loading');
            
            try {
                const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=${apiKey}`;
                
                const requestBody = {
                    contents: [{
                        parts: [{
                            text: 'Hello'
                        }]
                    }],
                    generationConfig: {
                        maxOutputTokens: 10,
                        temperature: 0.1,
                        thinkingConfig: {
                            thinkingBudget: 0
                        }
                    }
                };

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();

                if (response.ok) {
                    showResult(`✅ 基础连接测试成功！
状态码: ${response.status}
响应数据: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`❌ 基础连接测试失败
状态码: ${response.status}
错误信息: ${JSON.stringify(data, null, 2)}`, 'error');
                }

            } catch (error) {
                showResult(`❌ 网络请求失败: ${error.message}`, 'error');
            }
        });

        // 获取模型列表测试
        elements.testModels.addEventListener('click', async () => {
            const apiKey = elements.apiKey.value.trim();
            if (!apiKey) {
                showResult('请先输入API密钥', 'error');
                return;
            }

            showResult('正在获取模型列表...', 'loading');
            
            try {
                const url = `https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`;
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    const models = data.models || [];
                    const modelNames = models.map(m => m.name).join('\n');
                    showResult(`✅ 模型列表获取成功！
可用模型数量: ${models.length}
模型列表:
${modelNames}`, 'success');
                } else {
                    showResult(`❌ 模型列表获取失败
状态码: ${response.status}
错误信息: ${JSON.stringify(data, null, 2)}`, 'error');
                }

            } catch (error) {
                showResult(`❌ 网络请求失败: ${error.message}`, 'error');
            }
        });

        // 生成内容测试
        elements.testGenerate.addEventListener('click', async () => {
            const apiKey = elements.apiKey.value.trim();
            if (!apiKey) {
                showResult('请先输入API密钥', 'error');
                return;
            }

            showResult('正在测试内容生成...', 'loading');
            
            try {
                const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=${apiKey}`;
                
                const requestBody = {
                    contents: [{
                        parts: [{
                            text: '请用中文简单介绍一下人工智能'
                        }]
                    }],
                    generationConfig: {
                        maxOutputTokens: 100,
                        temperature: 0.7,
                        thinkingConfig: {
                            thinkingBudget: 0
                        }
                    }
                };

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();

                if (response.ok && data.candidates && data.candidates[0]) {
                    const generatedText = data.candidates[0].content.parts[0].text;
                    showResult(`✅ 内容生成测试成功！
生成的内容:
${generatedText}

完整响应:
${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`❌ 内容生成测试失败
状态码: ${response.status}
错误信息: ${JSON.stringify(data, null, 2)}`, 'error');
                }

            } catch (error) {
                showResult(`❌ 网络请求失败: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
