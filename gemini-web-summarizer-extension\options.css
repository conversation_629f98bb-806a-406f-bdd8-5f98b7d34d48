/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
}

.header-content h1 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
}

.subtitle {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 300;
}

/* 主内容区域 */
.main-content {
    padding: 30px;
}

.settings-container {
    max-width: 100%;
}

/* 设置区块样式 */
.settings-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
}

.section-header {
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 15px;
}

.section-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.section-description {
    color: #6c757d;
    font-size: 14px;
}

/* 表单组样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group:last-child {
    margin-bottom: 0;
}

label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;
}

/* 输入框样式 */
input[type="text"],
input[type="password"],
select,
textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
    font-family: inherit;
}

input[type="text"]:focus,
input[type="password"]:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* 文本域特殊样式 */
textarea {
    resize: vertical;
    min-height: 120px;
    line-height: 1.5;
}

textarea::placeholder {
    color: #6c757d;
    font-size: 13px;
    line-height: 1.4;
}

/* 输入组样式 */
.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group input {
    flex: 1;
    padding-right: 50px;
}

.toggle-btn {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.toggle-btn:hover {
    background: #f8f9fa;
}

/* 复选框样式 */
.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #007bff;
    cursor: pointer;
}

.checkbox-group label {
    margin-bottom: 0;
    cursor: pointer;
    font-weight: 500;
}

/* 滑块样式 */
.slider-group {
    display: flex;
    align-items: center;
    gap: 15px;
}

input[type="range"] {
    flex: 1;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: #007bff;
    border-radius: 50%;
    cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #007bff;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

#temperature-value {
    font-weight: 600;
    color: #007bff;
    min-width: 30px;
    text-align: center;
}

/* 按钮样式 */
.primary-btn,
.secondary-btn,
.danger-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.primary-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.secondary-btn {
    background: #6c757d;
    color: white;
}

.secondary-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.danger-btn {
    background: #dc3545;
    color: white;
}

.danger-btn:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.button-group button {
    flex: 1;
    min-width: 120px;
}

/* 加载状态 */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 帮助文本样式 */
.form-help {
    margin-top: 8px;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.form-help a {
    color: #007bff;
    text-decoration: none;
}

.form-help a:hover {
    text-decoration: underline;
}

/* 验证结果样式 */
.validation-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 6px;
    font-size: 14px;
    display: none;
}

.validation-result.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.validation-result.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 统计网格样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    background: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #007bff;
}

/* 关于部分样式 */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    align-items: start;
}

.version-info p {
    margin-bottom: 8px;
    font-size: 14px;
}

.links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.link-btn {
    display: inline-block;
    padding: 8px 16px;
    background: #f8f9fa;
    color: #495057;
    text-decoration: none;
    border-radius: 6px;
    font-size: 14px;
    transition: background 0.3s ease;
    text-align: center;
}

.link-btn:hover {
    background: #e9ecef;
}

/* 保存区域样式 */
.save-section {
    position: sticky;
    bottom: 0;
    background: white;
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.save-status {
    margin-top: 10px;
    font-size: 14px;
    font-weight: 500;
}

.save-status.success {
    color: #28a745;
}

.save-status.error {
    color: #dc3545;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 12px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-content h3 {
    margin-bottom: 15px;
    color: #495057;
}

.modal-content p {
    margin-bottom: 25px;
    color: #6c757d;
    line-height: 1.5;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.modal-buttons button {
    min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 0;
        border-radius: 0;
    }
    
    .header {
        padding: 30px 20px;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .settings-section {
        padding: 20px;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .button-group button {
        min-width: auto;
    }
    
    .save-section {
        padding: 15px 20px;
    }
}
