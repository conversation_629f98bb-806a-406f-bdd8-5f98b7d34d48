/**
 * 下载Mermaid库的脚本
 * 运行: node download-mermaid.js
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

const MERMAID_URL = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js';
const OUTPUT_PATH = path.join(__dirname, 'lib', 'mermaid.min.js');

// 确保lib目录存在
const libDir = path.dirname(OUTPUT_PATH);
if (!fs.existsSync(libDir)) {
    fs.mkdirSync(libDir, { recursive: true });
}

console.log('正在下载Mermaid库...');

https.get(MERMAID_URL, (response) => {
    if (response.statusCode !== 200) {
        console.error('下载失败:', response.statusCode);
        return;
    }

    const file = fs.createWriteStream(OUTPUT_PATH);
    response.pipe(file);

    file.on('finish', () => {
        file.close();
        console.log('Mermaid库下载完成:', OUTPUT_PATH);
    });

    file.on('error', (err) => {
        fs.unlink(OUTPUT_PATH, () => {}); // 删除部分下载的文件
        console.error('写入文件失败:', err);
    });

}).on('error', (err) => {
    console.error('下载失败:', err);
});
